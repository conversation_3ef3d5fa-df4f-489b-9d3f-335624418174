import 'package:flutter/material.dart';

class AppTheme {
  // Color Palette based on screenshots
  static const Color primaryBlue = Color(0xFF87CEEB); // Light blue from splash
  static const Color darkBlue = Color(0xFF2E3A59); // Dark blue from categories
  static const Color tealGreen = Color(0xFF4ECDC4); // Teal from training
  static const Color lightGreen = Color(0xFF95C93D); // Light green from religion
  static const Color darkGreen = Color(0xFF2D5016); // Dark green
  static const Color pink = Color(0xFFE91E63); // Pink from book lovers
  static const Color purple = Color(0xFF673AB7); // Purple from academic
  
  // Background colors
  static const Color splashBackground = Color(0xFF87CEEB);
  static const Color categoryBackground = Color(0xFF5F8A8B);
  static const Color academicBackground = Color(0xFF5F8A8B);
  static const Color trainingBackground = Color(0xFF2E3A59);
  static const Color religionBackground = Color(0xFFB8D4A3);
  
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryBlue,
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      cardTheme: const CardThemeData(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        headlineMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: Colors.white,
        ),
      ),
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryBlue,
        brightness: Brightness.dark,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
