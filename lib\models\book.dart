import 'package:equatable/equatable.dart';

class Book extends Equatable {
  final String id;
  final String title;
  final String pdfUrl;
  final String categoryId;
  final String? institution;
  final String? program;
  final String? subject;
  final int? pageCount;
  final String? author;
  final String? description;
  final String? thumbnailUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isDownloaded;
  final String? localPath;

  const Book({
    required this.id,
    required this.title,
    required this.pdfUrl,
    required this.categoryId,
    this.institution,
    this.program,
    this.subject,
    this.pageCount,
    this.author,
    this.description,
    this.thumbnailUrl,
    this.createdAt,
    this.updatedAt,
    this.isDownloaded = false,
    this.localPath,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'] as String,
      title: json['title'] as String,
      pdfUrl: json['pdf_url'] as String,
      categoryId: json['category_id'] as String,
      institution: json['institution'] as String?,
      program: json['program'] as String?,
      subject: json['subject'] as String?,
      pageCount: json['page_count'] as int?,
      author: json['author'] as String?,
      description: json['description'] as String?,
      thumbnailUrl: json['thumbnail_url'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      isDownloaded: json['is_downloaded'] as bool? ?? false,
      localPath: json['local_path'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'pdf_url': pdfUrl,
      'category_id': categoryId,
      'institution': institution,
      'program': program,
      'subject': subject,
      'page_count': pageCount,
      'author': author,
      'description': description,
      'thumbnail_url': thumbnailUrl,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_downloaded': isDownloaded,
      'local_path': localPath,
    };
  }

  Book copyWith({
    String? id,
    String? title,
    String? pdfUrl,
    String? categoryId,
    String? institution,
    String? program,
    String? subject,
    int? pageCount,
    String? author,
    String? description,
    String? thumbnailUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDownloaded,
    String? localPath,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      categoryId: categoryId ?? this.categoryId,
      institution: institution ?? this.institution,
      program: program ?? this.program,
      subject: subject ?? this.subject,
      pageCount: pageCount ?? this.pageCount,
      author: author ?? this.author,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      localPath: localPath ?? this.localPath,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        pdfUrl,
        categoryId,
        institution,
        program,
        subject,
        pageCount,
        author,
        description,
        thumbnailUrl,
        createdAt,
        updatedAt,
        isDownloaded,
        localPath,
      ];
}
