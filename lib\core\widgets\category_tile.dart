import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../models/category.dart';

class CategoryTile extends StatelessWidget {
  final Category category;
  final VoidCallback onTap;
  final int? animationDelay;
  final bool isMainCategory;

  const CategoryTile({
    super.key,
    required this.category,
    required this.onTap,
    this.animationDelay,
    this.isMainCategory = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: isMainCategory ? 80 : 60,
        decoration: BoxDecoration(
          color: Color(category.color ?? 0xFF673AB7),
          borderRadius: BorderRadius.circular(isMainCategory ? 16 : 12),
          border: isMainCategory ? null : Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(isMainCategory ? 0.2 : 0.1),
              blurRadius: isMainCategory ? 8 : 6,
              offset: Offset(0, isMainCategory ? 4 : 3),
            ),
          ],
        ),
        child: Center(
          child: Text(
            category.name,
            style: TextStyle(
              fontSize: isMainCategory ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 1,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    )
        .animate(delay: Duration(milliseconds: animationDelay ?? 0))
        .slideX(
          begin: 0.2,
          duration: 250.ms,
          curve: Curves.easeOut,
        )
        .fadeIn(duration: 250.ms);
  }
}

class AnimatedCategoryGrid extends StatelessWidget {
  final List<Category> categories;
  final Function(Category) onCategoryTap;
  final bool isMainCategories;

  const AnimatedCategoryGrid({
    super.key,
    required this.categories,
    required this.onCategoryTap,
    this.isMainCategories = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMainCategories) {
      return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          childAspectRatio: 4,
          mainAxisSpacing: 20,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return CategoryTile(
            category: category,
            onTap: () => onCategoryTap(category),
            animationDelay: 100 + (index * 50),
            isMainCategory: true,
          );
        },
      );
    } else {
      return ListView.builder(
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: CategoryTile(
              category: category,
              onTap: () => onCategoryTap(category),
              animationDelay: 150 + (index * 50),
              isMainCategory: false,
            ),
          );
        },
      );
    }
  }
}
