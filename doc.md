# 🧭 Overview: “Readtap”

**ReadTap** is a cross-platform reading platform designed for **students, educators, and readers** across **academia, training sectors, religious studies**, and **literature**. It organizes content by institution, program, and subject — allowing users to **browse, download, and read PDFs** with top-tier features like offline caching, bookmarks, auto-scroll, and more.

---

# 🚀 Key Goals

* ✨ Ship-ready polished UI with **smooth animations**
* ⚙️ Highly stable and testable architecture
* 🛡️ Secure and optimized Supabase integration
* 📲 High-performance offline reading with native features
* 📖 Modern PDF viewing with intuitive controls

---

# 🛠️ Core Tech Stack

| Layer            | Stack                             |
| ---------------- | --------------------------------- |
| UI Framework     | Flutter 3.22+                     |
| State Management | `flutter_bloc 8.x`                |
| Backend          | Supabase (Auth, Storage, DB)      |
| PDF Viewer       | Syncfusion or AdvancePdfViewer    |
| Local DB         | Hive or Isar (for offline access) |
| Routing          | `go_router 13.x`                  |
| Animations       | `animations`, `flutter_animate`   |
| CI/CD            | Codemagic                         |

---

# 📁 Folder Structure (Bloc-Centric)

```bash
/lib
  /core
    /config         # App config, themes, env
    /services       # Supabase, cache, download
    /utils          # Helpers, extensions
    /widgets        # Reusable UI components
  /features
    /auth
      /bloc         # LoginBloc, AuthBloc
      /views        # Login, Signup screens
    /home
    /reader
    /categories
    /profile
    /bookmarks
    /offline
  /models
  main.dart
```

---

# 🔄 App Flow

### 1. **Splash & Auth Check**

* Show logo → check auth → navigate
* Use animated splash transitions

### 2. **Onboarding**

* Choose interest: Academic / Training / Religion / Book Lovers
* Save to Supabase or local (Hive)

### 3. **Login / Sign Up**

* Email/password (Supabase Auth)
* Secure session handling with BLoC
* Future-ready for Google OAuth

### 4. **Main Menu**

> Grid of 4 Categories:

* **Academic**
* **Training**
* **Religion**
* **Book Lovers**

Each tile animated in with staggered entrance.

---

## 🧠 BLoC Architecture (Per Feature)

Each module (auth, reader, bookmarks, etc.) has:

```
- Cubit or Bloc
- State
- Event (if using Bloc)
```

✅ Follows clean architecture

Example:

```dart
class BookBloc extends Bloc<BookEvent, BookState> {
  BookBloc(this.bookRepo) : super(BookInitial()) {
    on<LoadBooks>((event, emit) async {
      emit(BookLoading());
      try {
        final books = await bookRepo.getBooks(event.categoryId);
        emit(BookLoaded(books));
      } catch (e) {
        emit(BookError("Could not load books"));
      }
    });
  }
}
```

---

# 🔐 Supabase Schema

### `users`

```sql
id UUID PRIMARY KEY,
email TEXT UNIQUE,
interest TEXT,
created_at TIMESTAMP
```

### `categories`

```sql
id UUID,
name TEXT,
type TEXT, -- academic, religion, etc.
parent_id UUID
```

### `books`

```sql
id UUID,
title TEXT,
pdf_url TEXT,
category_id UUID,
institution TEXT,
program TEXT,
subject TEXT,
page_count INT
```

### `bookmarks`

```sql
id UUID,
user_id UUID,
book_id UUID,
page_number INT,
note TEXT
```

---

# 📲 PDF Reader Design

### Powered by `Syncfusion` (or `advance_pdf_viewer`)

* **Smooth vertical/horizontal scroll**
* **Zoom gestures**
* **Auto-scroll toggle**
* **Page navigation slider**
* **Highlight/marker**
* **Eraser icon**
* **Dark/light mode toggle**
* **Custom AppBar with page no. & progress**

### Bloc Integration

* `ReaderBloc` tracks scroll, current page, zoom
* `BookmarkBloc` for adding/removing bookmarks
* Store current page locally for resume

---

# 🔁 Offline Support

### 🔹 What is cached?

* PDFs (via `path_provider`)
* Metadata (books, categories)
* Bookmarks (in Hive)
* Last-read page & history

### 🔹 Packages

* `hive`, `hive_flutter`
* `dio` for downloading PDFs

---

# 🎨 UI / UX & Animations

### ✅ Entry & Navigation Animations

* `flutter_animate` or `animations` for fade, slide, scale
* Tab/category tile entry: staggered slide
* Page transitions: fade-through

### ✅ Microinteractions

* Haptic feedback on buttons
* Smooth scroll glows
* Pull-to-refresh bounce
* Book open → animated page flip transition

---

# 🔎 Search & Filter (Phase 2)

* Search across title, program, subject
* Filter by institution
* Possibly use Supabase full-text search

---

# 🧩 Modularity

Each major module is **independent** with its own:

* Repositories
* Blocs/Cubits
* Views
* Widgets

E.g., `categories`, `books`, `reader`, `offline`, `auth`

This ensures maintainability and scalable growth.

---

# 🧪 Testing Plan

| Type         | Tools / Frameworks           |
| ------------ | ---------------------------- |
| Unit tests   | Blocs, Repositories          |
| Widget tests | UI screens & animations      |
| Integration  | Auth flow, book reading flow |
| Offline mode | Network simulation           |

Use `mocktail` for mocking, `bloc_test` for Bloc tests.

---

# 🧳 CI/CD

### 🔧 Codemagic

* Android + iOS builds
* Inject Supabase keys securely
* Auto-versioning
* Post-build Slack/Email alerts

---

# 🧱 Supabase Storage Layout

```
/storage/books/
  academic/university/nsu/bba/act201.pdf
  religion/islam/tafsir.pdf
  training/business/leadership_101.pdf
```

Generate public/signed URLs via Supabase SDK.

---

# ⚙️ App Settings

Stored locally:

* Theme (dark/light)
* Auto-scroll toggle
* Font size preference
* Last-read book & page

Stored using `Hive`.

---

# 📦 Publish Readiness Checklist

✅ Platform: Android, iOS
✅ Performance: 60 FPS scroll, memory-safe
✅ Offline mode: Downloaded books open w/o internet
✅ Security: Supabase Row-Level Security enabled
✅ Modular Code: Scalable BLoC structure
✅ CI/CD: Automated build/test/deploy
✅ Polish: Animations, Haptics, UX fluidity

---

# ✅ Final Notes

> **“ReadTap”** is now designed as a ship-ready, full-featured reading app backed by BLoC architecture for reliability and performance, Supabase for scalable backend, and refined UI/UX for a top-tier user experience.
