import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/config/app_theme.dart';
import 'core/config/app_router.dart';
import 'core/services/supabase_service.dart';
import 'core/services/deep_link_service.dart';
import 'features/auth/bloc/auth_cubit.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize services
  await SupabaseService().initialize();
  DeepLinkService().initialize();

  // Handle initial deep link (if app was opened via deep link)
  final initialLink = await DeepLinkService().getInitialLink();
  if (initialLink != null) {
    print('App opened with deep link: $initialLink');
  }

  runApp(const ReadTapApp());
}

class ReadTapApp extends StatelessWidget {
  const ReadTapApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AuthCubit(SupabaseService()),
      child: MaterialApp.router(
        title: 'ReadTap',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.light,
        routerConfig: AppRouter.router,
      ),
    );
  }
}
