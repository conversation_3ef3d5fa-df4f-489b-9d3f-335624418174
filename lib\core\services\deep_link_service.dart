import 'package:flutter/services.dart';
import 'dart:async';

class DeepLinkService {
  static const MethodChannel _channel = MethodChannel('com.readtap.app/deeplink');
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  final StreamController<String> _linkStreamController = StreamController<String>.broadcast();
  
  Stream<String> get linkStream => _linkStreamController.stream;
  
  void initialize() {
    _channel.setMethodCallHandler(_handleMethodCall);

    // Listen for deep links and handle OAuth callbacks
    linkStream.listen((String link) {
      print('Received deep link: $link');
      _handleOAuthCallback(link);
    });
  }

  void _handleOAuthCallback(String url) {
    final params = parseOAuthCallback(url);
    if (params != null) {
      print('OAuth callback detected with params: $params');
      // The Supabase client will automatically handle the session
    }
  }

  Future<String?> getInitialLink() async {
    try {
      final String? link = await _channel.invokeMethod('getInitialLink');
      return link;
    } on PlatformException catch (e) {
      print('Failed to get initial link: ${e.message}');
      return null;
    }
  }

  Future<void> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onDeepLink':
        final String link = call.arguments;
        _linkStreamController.add(link);
        break;
      default:
        throw MissingPluginException('Not implemented: ${call.method}');
    }
  }

  void dispose() {
    _linkStreamController.close();
  }

  // Helper method to parse OAuth callback URLs
  static Map<String, String>? parseOAuthCallback(String url) {
    try {
      final uri = Uri.parse(url);
      
      // Check if this is a Supabase OAuth callback
      if (uri.scheme == 'com.readtap.app' || uri.scheme == 'io.supabase.readtap') {
        final fragment = uri.fragment;
        if (fragment.isNotEmpty) {
          final params = <String, String>{};
          final pairs = fragment.split('&');
          
          for (final pair in pairs) {
            final keyValue = pair.split('=');
            if (keyValue.length == 2) {
              params[keyValue[0]] = Uri.decodeComponent(keyValue[1]);
            }
          }
          
          return params;
        }
      }
      
      return null;
    } catch (e) {
      print('Error parsing OAuth callback: $e');
      return null;
    }
  }
}
