import 'package:equatable/equatable.dart';

class Category extends Equatable {
  final String id;
  final String name;
  final String type;
  final String? parentId;
  final String? description;
  final String? iconPath;
  final int? color;

  const Category({
    required this.id,
    required this.name,
    required this.type,
    this.parentId,
    this.description,
    this.iconPath,
    this.color,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      parentId: json['parent_id'] as String?,
      description: json['description'] as String?,
      iconPath: json['icon_path'] as String?,
      color: json['color'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'parent_id': parentId,
      'description': description,
      'icon_path': iconPath,
      'color': color,
    };
  }

  Category copyWith({
    String? id,
    String? name,
    String? type,
    String? parentId,
    String? description,
    String? iconPath,
    int? color,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      parentId: parentId ?? this.parentId,
      description: description ?? this.description,
      iconPath: iconPath ?? this.iconPath,
      color: color ?? this.color,
    );
  }

  @override
  List<Object?> get props => [id, name, type, parentId, description, iconPath, color];
}

// Predefined categories based on screenshots
class CategoryData {
  static const List<Category> mainCategories = [
    Category(
      id: 'academic',
      name: 'ACADEMIC',
      type: 'main',
      color: 0xFF673AB7, // Purple
    ),
    Category(
      id: 'training',
      name: 'TRAINING',
      type: 'main',
      color: 0xFF4ECDC4, // Teal
    ),
    Category(
      id: 'religion',
      name: 'RELIGION',
      type: 'main',
      color: 0xFF95C93D, // Light Green
    ),
    Category(
      id: 'book_lovers',
      name: 'BOOK\nLOVERS',
      type: 'main',
      color: 0xFFE91E63, // Pink
    ),
  ];

  static const List<Category> academicSubcategories = [
    Category(
      id: 'school',
      name: 'SCHOOL',
      type: 'academic',
      parentId: 'academic',
      color: 0xFF2196F3, // Blue
    ),
    Category(
      id: 'college',
      name: 'COLLEGE',
      type: 'academic',
      parentId: 'academic',
      color: 0xFF1976D2, // Darker Blue
    ),
    Category(
      id: 'english_medium',
      name: 'ENGLISH MEDIUM',
      type: 'academic',
      parentId: 'academic',
      color: 0xFF3F51B5, // Indigo
    ),
    Category(
      id: 'university',
      name: 'UNIVERSITY',
      type: 'academic',
      parentId: 'academic',
      color: 0xFF673AB7, // Purple
    ),
  ];

  static const List<Category> trainingSubcategories = [
    Category(
      id: 'technology',
      name: 'Technology',
      type: 'training',
      parentId: 'training',
      color: 0xFF4ECDC4, // Teal
    ),
    Category(
      id: 'business',
      name: 'Business',
      type: 'training',
      parentId: 'training',
      color: 0xFF26A69A, // Darker Teal
    ),
    Category(
      id: 'soft_skills',
      name: 'Soft Skills',
      type: 'training',
      parentId: 'training',
      color: 0xFF00897B, // Even Darker Teal
    ),
    Category(
      id: 'language',
      name: 'Language',
      type: 'training',
      parentId: 'training',
      color: 0xFF00695C, // Darkest Teal
    ),
  ];

  static const List<Category> religionSubcategories = [
    Category(
      id: 'islam',
      name: 'ISLAM',
      type: 'religion',
      parentId: 'religion',
      color: 0xFF8BC34A, // Light Green
    ),
    Category(
      id: 'christianity',
      name: 'CHRISTIANITY',
      type: 'religion',
      parentId: 'religion',
      color: 0xFF689F38, // Medium Green
    ),
    Category(
      id: 'hinduism',
      name: 'HINDUISM',
      type: 'religion',
      parentId: 'religion',
      color: 0xFF558B2F, // Darker Green
    ),
    Category(
      id: 'buddhism',
      name: 'BUDDHISM',
      type: 'religion',
      parentId: 'religion',
      color: 0xFF33691E, // Darkest Green
    ),
  ];
}
