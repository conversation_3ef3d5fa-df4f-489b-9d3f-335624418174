import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import '../../../core/config/app_theme.dart';
import '../../../core/config/app_router.dart';
import '../../../models/category.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.categoryBackground,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // App Logo (smaller version)
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF673AB7), // Purple
                      Color(0xFF4ECDC4), // Teal
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.menu_book_rounded,
                  size: 40,
                  color: Colors.white,
                ),
              )
                  .animate()
                  .scale(duration: 300.ms, curve: Curves.easeOutBack)
                  .fadeIn(duration: 200.ms),
              
              const SizedBox(height: 20),
              
              Text(
                'READTAP',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 2,
                  color: Colors.white,
                ),
              )
                  .animate(delay: 100.ms)
                  .fadeIn(duration: 300.ms)
                  .slideY(begin: 0.1, duration: 300.ms),
              
              const SizedBox(height: 60),
              
              // Category Grid
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 1,
                    childAspectRatio: 4,
                    mainAxisSpacing: 20,
                  ),
                  itemCount: CategoryData.mainCategories.length,
                  itemBuilder: (context, index) {
                    final category = CategoryData.mainCategories[index];
                    return _CategoryTile(
                      category: category,
                      index: index,
                      onTap: () => _navigateToCategory(context, category.id),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCategory(BuildContext context, String categoryId) {
    switch (categoryId) {
      case 'academic':
        context.go(AppRouter.academic);
        break;
      case 'training':
        context.go(AppRouter.training);
        break;
      case 'religion':
        context.go(AppRouter.religion);
        break;
      case 'book_lovers':
        // TODO: Implement book lovers screen
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Book Lovers section coming soon!')),
        );
        break;
    }
  }
}

class _CategoryTile extends StatelessWidget {
  final Category category;
  final int index;
  final VoidCallback onTap;

  const _CategoryTile({
    required this.category,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Color(category.color ?? 0xFF673AB7),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Text(
            category.name,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 1,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    )
        .animate(delay: (100 + (index * 50)).ms)
        .slideX(
          begin: -0.2,
          duration: 300.ms,
          curve: Curves.easeOut,
        )
        .fadeIn(duration: 300.ms);
  }
}
