import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../config/app_theme.dart';

class AppLogo extends StatelessWidget {
  final double size;
  final bool showText;
  final bool animate;
  final String? text;

  const AppLogo({
    super.key,
    this.size = 120,
    this.showText = true,
    this.animate = true,
    this.text,
  });

  @override
  Widget build(BuildContext context) {
    Widget logo = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Logo Container with Custom Image
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(size * 0.16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: size * 0.16,
                offset: Offset(0, size * 0.08),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(size * 0.16),
            child: Image.asset(
              'assets/images/logo.png',
              width: size,
              height: size,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                // Fallback to gradient container with icon if image fails to load
                return Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(size * 0.16),
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppTheme.purple,
                        AppTheme.tealGreen,
                      ],
                    ),
                  ),
                  child: Icon(
                    Icons.menu_book_rounded,
                    size: size * 0.5,
                    color: Colors.white,
                  ),
                );
              },
            ),
          ),
        ),
        
        if (showText) ...[
          SizedBox(height: size * 0.33),
          Text(
            text ?? 'READTAP',
            style: TextStyle(
              fontSize: size * 0.3,
              fontWeight: FontWeight.bold,
              letterSpacing: size * 0.033,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.black.withOpacity(0.3),
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
        ],
      ],
    );

    if (animate) {
      return logo
          .animate()
          .scale(
            duration: 800.ms,
            curve: Curves.elasticOut,
          )
          .fadeIn(duration: 600.ms);
    }

    return logo;
  }
}

class AnimatedAppTitle extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Color? color;
  final double? fontSize;
  final int delay;

  const AnimatedAppTitle({
    super.key,
    required this.title,
    this.subtitle,
    this.color,
    this.fontSize,
    this.delay = 400,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: fontSize ?? 36,
            fontWeight: FontWeight.bold,
            letterSpacing: 4,
            color: color ?? Colors.white,
            shadows: [
              Shadow(
                color: Colors.black.withOpacity(0.3),
                offset: const Offset(0, 2),
                blurRadius: 4,
              ),
            ],
          ),
        )
            .animate(delay: Duration(milliseconds: delay))
            .slideY(
              begin: 0.3,
              duration: 600.ms,
              curve: Curves.easeOut,
            )
            .fadeIn(duration: 600.ms),
        
        if (subtitle != null) ...[
          const SizedBox(height: 16),
          Text(
            subtitle!,
            style: TextStyle(
              fontSize: 16,
              color: (color ?? Colors.white).withOpacity(0.9),
              letterSpacing: 1,
            ),
          )
              .animate(delay: Duration(milliseconds: delay + 400))
              .slideY(
                begin: 0.3,
                duration: 600.ms,
                curve: Curves.easeOut,
              )
              .fadeIn(duration: 600.ms),
        ],
      ],
    );
  }
}
