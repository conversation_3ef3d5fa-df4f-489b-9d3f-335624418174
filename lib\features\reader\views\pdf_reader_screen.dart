import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:async';

enum AnnotationTool {
  none,
  highlighter,
  pen,
  eraser,
  text,
}

class PdfReaderScreen extends StatefulWidget {
  final String bookId;
  final String bookTitle;

  const PdfReaderScreen({
    super.key,
    required this.bookId,
    required this.bookTitle,
  });

  @override
  State<PdfReaderScreen> createState() => _PdfReaderScreenState();
}

class _PdfReaderScreenState extends State<PdfReaderScreen>
    with TickerProviderStateMixin {
  final PdfViewerController _pdfViewerController = PdfViewerController();
  late AnimationController _controlsAnimationController;
  late AnimationController _fabAnimationController;
  late AnimationController _bottomNavAnimationController;

  bool _isAutoScrolling = false;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  final double _zoomLevel = 1.0;
  Timer? _autoScrollTimer;
  Timer? _hideControlsTimer;
  final bool _isLoading = true;

  // Annotation tools
  AnnotationTool _selectedTool = AnnotationTool.none;
  Color _selectedColor = Colors.yellow;
  double _penSize = 3.0;
  bool _showColorPicker = false;

  @override
  void initState() {
    super.initState();
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _bottomNavAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _controlsAnimationController.forward();
    _fabAnimationController.forward();
    _bottomNavAnimationController.forward();
    _startHideControlsTimer();

    // Set immersive mode
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _controlsAnimationController.dispose();
    _fabAnimationController.dispose();
    _bottomNavAnimationController.dispose();
    _autoScrollTimer?.cancel();
    _hideControlsTimer?.cancel();

    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        _hideControls();
      }
    });
  }

  void _showControlsTemporarily() {
    if (!_showControls) {
      _showControlsWithAnimation();
    }
    _startHideControlsTimer();
  }

  void _showControlsWithAnimation() {
    setState(() {
      _showControls = true;
    });
    _controlsAnimationController.forward();
    _fabAnimationController.forward();
    _bottomNavAnimationController.forward();
  }

  void _hideControls() {
    _controlsAnimationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
    _fabAnimationController.reverse();
    _bottomNavAnimationController.reverse();
  }

  // Annotation tool methods
  void _selectTool(AnnotationTool tool) {
    if (!mounted) return;

    setState(() {
      _selectedTool = tool;
    });

    HapticFeedback.lightImpact();
    _showSnackBar(_getToolMessage(tool), _getToolIcon(tool));
  }

  void _toggleColorPicker() {
    if (!mounted) return;

    setState(() {
      _showColorPicker = !_showColorPicker;
    });

    HapticFeedback.lightImpact();
  }

  void _selectColor(Color color) {
    if (!mounted) return;

    setState(() {
      _selectedColor = color;
      _showColorPicker = false;
    });

    HapticFeedback.lightImpact();
  }

  void _changePenSize(double size) {
    if (!mounted) return;

    setState(() {
      _penSize = size;
    });
  }

  String _getToolMessage(AnnotationTool tool) {
    switch (tool) {
      case AnnotationTool.highlighter:
        return 'Highlighter selected';
      case AnnotationTool.pen:
        return 'Pen selected';
      case AnnotationTool.eraser:
        return 'Eraser selected';
      case AnnotationTool.text:
        return 'Text tool selected';
      case AnnotationTool.none:
        return 'Tool deselected';
    }
  }

  IconData _getToolIcon(AnnotationTool tool) {
    switch (tool) {
      case AnnotationTool.highlighter:
        return Icons.highlight;
      case AnnotationTool.pen:
        return Icons.edit;
      case AnnotationTool.eraser:
        return Icons.cleaning_services;
      case AnnotationTool.text:
        return Icons.text_fields;
      case AnnotationTool.none:
        return Icons.touch_app;
    }
  }

  void _toggleAutoScroll() {
    if (!mounted) return;

    try {
      setState(() {
        _isAutoScrolling = !_isAutoScrolling;
      });

      if (_isAutoScrolling) {
        _startAutoScroll();
      } else {
        _stopAutoScroll();
      }

      HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Auto-scroll toggle error: $e');
    }
  }

  void _startAutoScroll() {
    _stopAutoScroll(); // Stop any existing timer

    _autoScrollTimer = Timer.periodic(const Duration(milliseconds: 2000), (timer) {
      if (!mounted || !_isAutoScrolling) {
        timer.cancel();
        return;
      }

      try {
        if (_currentPage < _totalPages) {
          _pdfViewerController.nextPage();
        } else {
          _stopAutoScroll();
        }
      } catch (e) {
        debugPrint('Auto-scroll error: $e');
        _stopAutoScroll();
      }
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
    _autoScrollTimer = null;

    if (mounted) {
      setState(() {
        _isAutoScrolling = false;
      });
    }
  }

  void _toggleControls() {
    if (!mounted) return;

    try {
      if (_showControls) {
        _hideControls();
        _hideControlsTimer?.cancel();
      } else {
        _showControlsWithAnimation();
        _startHideControlsTimer();
      }
    } catch (e) {
      // Silently handle any gesture conflicts
      debugPrint('Toggle controls error: $e');
    }
  }

  void _addBookmark() {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.bookmark_added, color: Colors.white),
            const SizedBox(width: 8),
            Text('Bookmark added for page $_currentPage'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showPageNavigator() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Go to Page',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Current Page',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '$_currentPage / $_totalPages',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.blue,
                inactiveTrackColor: Colors.grey[300],
                thumbColor: Colors.blue,
                overlayColor: Colors.blue.withOpacity(0.2),
                trackHeight: 4,
              ),
              child: Slider(
                value: _currentPage.toDouble(),
                min: 1,
                max: _totalPages.toDouble(),
                divisions: _totalPages > 1 ? _totalPages - 1 : 1,
                label: _currentPage.toString(),
                onChanged: (value) {
                  setState(() {
                    _currentPage = value.round();
                  });
                },
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _pdfViewerController.jumpToPage(_currentPage);
                      Navigator.pop(context);
                      HapticFeedback.lightImpact();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Go'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: _showControls
          ? PreferredSize(
              preferredSize: const Size.fromHeight(kToolbarHeight),
              child: AnimatedBuilder(
                animation: _controlsAnimationController,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      0,
                      -kToolbarHeight * (1 - _controlsAnimationController.value),
                    ),
                    child: AppBar(
                      backgroundColor: Colors.black.withOpacity(0.8),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      title: Text(
                        widget.bookTitle,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      leading: IconButton(
                        icon: const Icon(Icons.arrow_back_ios_new, size: 20),
                        onPressed: () {
                          try {
                            if (context.canPop()) {
                              context.pop();
                            } else {
                              context.go('/home');
                            }
                          } catch (e) {
                            Navigator.of(context).pop();
                          }
                        },
                      ),
                      actions: [
                        // Page indicator
                        Center(
                          child: GestureDetector(
                            onTap: _showPageNavigator,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '$_currentPage / $_totalPages',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // More options
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.more_vert, color: Colors.white),
                          color: Colors.grey[900],
                          onSelected: (value) {
                            switch (value) {
                              case 'highlight':
                                _showSnackBar('Highlight tool activated', Icons.highlight);
                                break;
                              case 'search':
                                _showSnackBar('Search feature coming soon', Icons.search);
                                break;
                              case 'settings':
                                _showSnackBar('Settings coming soon', Icons.settings);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'highlight',
                              child: Row(
                                children: [
                                  Icon(Icons.highlight, color: Colors.white, size: 20),
                                  SizedBox(width: 12),
                                  Text('Highlight', style: TextStyle(color: Colors.white)),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'search',
                              child: Row(
                                children: [
                                  Icon(Icons.search, color: Colors.white, size: 20),
                                  SizedBox(width: 12),
                                  Text('Search', style: TextStyle(color: Colors.white)),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'settings',
                              child: Row(
                                children: [
                                  Icon(Icons.settings, color: Colors.white, size: 20),
                                  SizedBox(width: 12),
                                  Text('Settings', style: TextStyle(color: Colors.white)),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                  );
                },
              ),
            )
          : null,
      body: Stack(
        children: [
          // PDF Viewer
          Container(
            color: Colors.black,
            child: SfPdfViewer.network(
              'https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf',
              controller: _pdfViewerController,
              canShowScrollHead: false,
              canShowScrollStatus: false,
              enableDoubleTapZooming: true,
              enableTextSelection: true,
              onPageChanged: (PdfPageChangedDetails details) {
                if (mounted) {
                  setState(() {
                    _currentPage = details.newPageNumber;
                  });
                  _startHideControlsTimer();
                }
              },
              onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                if (mounted) {
                  setState(() {
                    _totalPages = details.document.pages.count;
                  });
                }
              },
            ),
          ),

          // Invisible tap detector for showing controls
          if (!_showControls)
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  if (mounted) {
                    _toggleControls();
                  }
                },
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            ),

          // Sleek Bottom Navigation Bar
          if (_showControls) ...[
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: AnimatedBuilder(
                animation: _bottomNavAnimationController,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      0,
                      100 * (1 - _bottomNavAnimationController.value),
                    ),
                    child: _buildBottomNavigationBar(),
                  );
                },
              ),
            ),
          ],

          // Color Picker Overlay
          if (_showColorPicker && _showControls)
            Positioned(
              bottom: 120,
              left: 20,
              right: 20,
              child: AnimatedBuilder(
                animation: _bottomNavAnimationController,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      0,
                      50 * (1 - _bottomNavAnimationController.value),
                    ),
                    child: _buildColorPicker(),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? Colors.blue.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isActive
                ? Colors.blue.withOpacity(0.5)
                : Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? Colors.blue : Colors.white,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.blue : Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.95),
          ],
        ),
      ),
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildNavButton(
              icon: Icons.highlight,
              label: 'Highlight',
              isActive: _selectedTool == AnnotationTool.highlighter,
              onTap: () => _selectTool(AnnotationTool.highlighter),
            ),
            _buildNavButton(
              icon: Icons.edit,
              label: 'Pen',
              isActive: _selectedTool == AnnotationTool.pen,
              onTap: () => _selectTool(AnnotationTool.pen),
            ),
            _buildNavButton(
              icon: Icons.cleaning_services,
              label: 'Eraser',
              isActive: _selectedTool == AnnotationTool.eraser,
              onTap: () => _selectTool(AnnotationTool.eraser),
            ),
            _buildNavButton(
              icon: Icons.palette,
              label: 'Color',
              isActive: _showColorPicker,
              onTap: _toggleColorPicker,
            ),
            _buildNavButton(
              icon: _isAutoScrolling ? Icons.pause : Icons.play_arrow,
              label: _isAutoScrolling ? 'Pause' : 'Auto',
              isActive: _isAutoScrolling,
              onTap: _toggleAutoScroll,
            ),
            _buildNavButton(
              icon: Icons.bookmark_add_outlined,
              label: 'Bookmark',
              isActive: false,
              onTap: _addBookmark,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: isActive
              ? Colors.blue.withOpacity(0.3)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: isActive
              ? Border.all(
                  color: Colors.blue.withOpacity(0.5),
                  width: 1,
                )
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? Colors.blue : Colors.white,
              size: 20,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.blue : Colors.white.withOpacity(0.8),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorPicker() {
    final colors = [
      Colors.yellow,
      Colors.green,
      Colors.blue,
      Colors.red,
      Colors.purple,
      Colors.orange,
      Colors.pink,
      Colors.cyan,
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Color',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: _toggleColorPicker,
                child: Icon(
                  Icons.close,
                  color: Colors.white.withOpacity(0.7),
                  size: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: colors.map((color) {
              final isSelected = _selectedColor == color;
              return GestureDetector(
                onTap: () => _selectColor(color),
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(
                            color: Colors.white,
                            width: 3,
                          )
                        : Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                    boxShadow: [
                      BoxShadow(
                        color: color.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text(
                'Size: ',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: _selectedColor,
                    inactiveTrackColor: Colors.grey[600],
                    thumbColor: _selectedColor,
                    overlayColor: _selectedColor.withOpacity(0.2),
                    trackHeight: 3,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 8,
                    ),
                  ),
                  child: Slider(
                    value: _penSize,
                    min: 1.0,
                    max: 10.0,
                    divisions: 9,
                    onChanged: _changePenSize,
                  ),
                ),
              ),
              Text(
                '${_penSize.round()}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, IconData icon) {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.grey[800],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
