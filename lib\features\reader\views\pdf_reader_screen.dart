import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:go_router/go_router.dart';

class PdfReaderScreen extends StatefulWidget {
  final String bookId;
  final String bookTitle;

  const PdfReaderScreen({
    super.key,
    required this.bookId,
    required this.bookTitle,
  });

  @override
  State<PdfReaderScreen> createState() => _PdfReaderScreenState();
}

class _PdfReaderScreenState extends State<PdfReaderScreen> {
  final PdfViewerController _pdfViewerController = PdfViewerController();
  bool _isAutoScrolling = false;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void dispose() {
    _pdfViewerController.dispose();
    super.dispose();
  }

  void _toggleAutoScroll() {
    setState(() {
      _isAutoScrolling = !_isAutoScrolling;
    });
    
    if (_isAutoScrolling) {
      _startAutoScroll();
    }
  }

  void _startAutoScroll() {
    // TODO: Implement auto-scroll functionality
    // This would require custom implementation with Timer
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  void _addBookmark() {
    // TODO: Implement bookmark functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Bookmark added for page $_currentPage'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showPageNavigator() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Go to Page'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current: $_currentPage / $_totalPages'),
            const SizedBox(height: 16),
            Slider(
              value: _currentPage.toDouble(),
              min: 1,
              max: _totalPages.toDouble(),
              divisions: _totalPages > 1 ? _totalPages - 1 : 1,
              label: _currentPage.toString(),
              onChanged: (value) {
                setState(() {
                  _currentPage = value.round();
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _pdfViewerController.jumpToPage(_currentPage);
              Navigator.pop(context);
            },
            child: const Text('Go'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _showControls ? AppBar(
        backgroundColor: Colors.black87,
        foregroundColor: Colors.white,
        title: Text(
          widget.bookTitle,
          style: const TextStyle(fontSize: 16),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          // Page indicator
          Center(
            child: GestureDetector(
              onTap: _showPageNavigator,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white24,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '$_currentPage / $_totalPages',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Marker/Highlight tool
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: Implement highlight/marker functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Marker tool activated')),
              );
            },
          ),
          // Eraser tool
          IconButton(
            icon: const Icon(Icons.cleaning_services),
            onPressed: () {
              // TODO: Implement eraser functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Eraser tool activated')),
              );
            },
          ),
        ],
      ) : null,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // PDF Viewer
            SfPdfViewer.network(
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf', // Sample PDF
              controller: _pdfViewerController,
              onPageChanged: (PdfPageChangedDetails details) {
                setState(() {
                  _currentPage = details.newPageNumber;
                });
              },
              onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                setState(() {
                  _totalPages = details.document.pages.count;
                });
              },
            ),
            
            // Floating Action Buttons
            if (_showControls) ...[
              // Auto-scroll toggle
              Positioned(
                right: 16,
                top: MediaQuery.of(context).size.height * 0.3,
                child: FloatingActionButton(
                  heroTag: "auto_scroll",
                  mini: true,
                  backgroundColor: _isAutoScrolling ? Colors.green : Colors.grey,
                  onPressed: _toggleAutoScroll,
                  child: Icon(
                    _isAutoScrolling ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                  ),
                ),
              ),
              
              // Bookmark button
              Positioned(
                right: 16,
                top: MediaQuery.of(context).size.height * 0.4,
                child: FloatingActionButton(
                  heroTag: "bookmark",
                  mini: true,
                  backgroundColor: Colors.blue,
                  onPressed: _addBookmark,
                  child: const Icon(
                    Icons.bookmark_add,
                    color: Colors.white,
                  ),
                ),
              ),
              
              // Zoom controls
              Positioned(
                right: 16,
                bottom: 100,
                child: Column(
                  children: [
                    FloatingActionButton(
                      heroTag: "zoom_in",
                      mini: true,
                      backgroundColor: Colors.black54,
                      onPressed: () {
                        setState(() {
                          _zoomLevel = (_zoomLevel + 0.25).clamp(0.5, 3.0);
                        });
                        _pdfViewerController.zoomLevel = _zoomLevel;
                      },
                      child: const Icon(Icons.zoom_in, color: Colors.white),
                    ),
                    const SizedBox(height: 8),
                    FloatingActionButton(
                      heroTag: "zoom_out",
                      mini: true,
                      backgroundColor: Colors.black54,
                      onPressed: () {
                        setState(() {
                          _zoomLevel = (_zoomLevel - 0.25).clamp(0.5, 3.0);
                        });
                        _pdfViewerController.zoomLevel = _zoomLevel;
                      },
                      child: const Icon(Icons.zoom_out, color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
