class AppConfig {
  static const String appName = 'ReadTap';
  static const String appVersion = '1.0.0';

  // Supabase Configuration - Singapore Region
  static const String supabaseUrl = 'https://cefmexpfuifebvnnnbkg.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNlZm1leHBmdWlmZWJ2bm5uYmtnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzOTcxNjYsImV4cCI6MjA2NDk3MzE2Nn0.XMHg9SG9z2_hBh-KiYJIGI85sSX1LoGyJMxCh25BLQo';

  // Storage Configuration
  static const String pdfsBucket = 'pdfs';
  static const String thumbnailsBucket = 'thumbnails';

  // App Settings
  static const int splashDuration = 2000; // milliseconds
  static const double defaultFontSize = 16.0;
  static const int maxBookmarksPerBook = 100;

  // PDF Reader Settings
  static const double defaultZoomLevel = 1.0;
  static const double maxZoomLevel = 5.0;
  static const double minZoomLevel = 0.5;
  static const int autoScrollSpeed = 50; // pixels per second

  // Database Settings
  static const int maxSearchResults = 50;
  static const int booksPerPage = 20;
  static const int maxAnnotationsPerPage = 100;

  // Cache Settings
  static const int maxCacheSize = 100; // MB
  static const int cacheExpiryDays = 7;
}
