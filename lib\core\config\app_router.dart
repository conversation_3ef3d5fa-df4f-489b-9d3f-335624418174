import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/splash/views/splash_screen.dart';
import '../../features/auth/views/login_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/categories/views/academic_categories_screen.dart';
import '../../features/categories/views/training_categories_screen.dart';
import '../../features/categories/views/religion_categories_screen.dart';
import '../../features/books/views/books_list_screen.dart';
import '../../features/reader/views/pdf_reader_screen.dart';
import '../../core/services/supabase_service.dart';

class AppRouter {
  static const String splash = '/';
  static const String login = '/login';
  static const String home = '/home';
  static const String academic = '/academic';
  static const String training = '/training';
  static const String religion = '/religion';
  static const String bookLovers = '/book-lovers';
  static const String books = '/books';
  static const String pdfReader = '/pdf-reader';

  static final SupabaseService _supabaseService = SupabaseService();
  
  static final GoRouter router = GoRouter(
    initialLocation: splash,
    redirect: (context, state) {
      final isAuthenticated = _supabaseService.isAuthenticated;
      final isOnLoginPage = state.matchedLocation == login;
      final isOnSplashPage = state.matchedLocation == splash;

      // If not authenticated and not on login/splash page, redirect to login
      if (!isAuthenticated && !isOnLoginPage && !isOnSplashPage) {
        return login;
      }

      // If authenticated and on login page, redirect to home
      if (isAuthenticated && isOnLoginPage) {
        return home;
      }

      return null; // No redirect needed
    },
    routes: [
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: home,
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: academic,
        name: 'academic',
        builder: (context, state) => const AcademicCategoriesScreen(),
      ),
      GoRoute(
        path: training,
        name: 'training',
        builder: (context, state) => const TrainingCategoriesScreen(),
      ),
      GoRoute(
        path: religion,
        name: 'religion',
        builder: (context, state) => const ReligionCategoriesScreen(),
      ),
      GoRoute(
        path: books,
        name: 'books',
        builder: (context, state) {
          final categoryId = state.uri.queryParameters['categoryId'] ?? '';
          return BooksListScreen(categoryId: categoryId);
        },
      ),
      GoRoute(
        path: pdfReader,
        name: 'pdf-reader',
        builder: (context, state) {
          final bookId = state.uri.queryParameters['bookId'] ?? '';
          final bookTitle = state.uri.queryParameters['title'] ?? 'Document';
          return PdfReaderScreen(
            bookId: bookId,
            bookTitle: bookTitle,
          );
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(home),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}
