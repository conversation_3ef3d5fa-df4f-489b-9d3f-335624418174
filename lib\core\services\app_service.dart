import '../../models/category.dart';
import '../../models/book.dart';
import 'supabase_service.dart';

class AppService {
  static final AppService _instance = AppService._internal();
  factory AppService() => _instance;
  AppService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Get main categories from Supabase
  Future<List<Category>> getMainCategories() async {
    try {
      return await _supabaseService.getMainCategories();
    } catch (e) {
      // Fallback to local data if Supabase fails
      return CategoryData.mainCategories;
    }
  }

  // Get subcategories by parent category from Supabase
  Future<List<Category>> getSubcategories(String parentCategoryId) async {
    try {
      return await _supabaseService.getSubcategories(parentCategoryId);
    } catch (e) {
      // Fallback to local data if Supabase fails
      switch (parentCategoryId) {
        case '550e8400-e29b-41d4-a716-446655440001': // Academic
          return CategoryData.academicSubcategories;
        case '550e8400-e29b-41d4-a716-446655440002': // Training
          return CategoryData.trainingSubcategories;
        case '550e8400-e29b-41d4-a716-446655440003': // Religion
          return CategoryData.religionSubcategories;
        default:
          return [];
      }
    }
  }

  // Get books by category from Supabase
  Future<List<Book>> getBooksByCategory(String categoryId) async {
    try {
      return await _supabaseService.getBooksByCategory(categoryId);
    } catch (e) {
      // Fallback to mock data if Supabase fails
      return _getMockBooks(categoryId);
    }
  }

  // Mock books data
  List<Book> _getMockBooks(String categoryId) {
    return [
      Book(
        id: '1',
        title: 'Introduction to ${_getCategoryDisplayName(categoryId)}',
        pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        categoryId: categoryId,
        author: 'Sample Author',
        description: 'A comprehensive guide to ${_getCategoryDisplayName(categoryId)}',
        pageCount: 150,
      ),
      Book(
        id: '2',
        title: 'Advanced ${_getCategoryDisplayName(categoryId)} Concepts',
        pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        categoryId: categoryId,
        author: 'Expert Author',
        description: 'Deep dive into advanced topics',
        pageCount: 250,
      ),
      Book(
        id: '3',
        title: '${_getCategoryDisplayName(categoryId)} Practical Guide',
        pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        categoryId: categoryId,
        author: 'Practical Author',
        description: 'Hands-on approach to learning',
        pageCount: 200,
      ),
    ];
  }

  String _getCategoryDisplayName(String categoryId) {
    final categoryNames = {
      'school': 'School Education',
      'college': 'College Studies',
      'english_medium': 'English Medium',
      'university': 'University',
      'technology': 'Technology',
      'business': 'Business',
      'soft_skills': 'Soft Skills',
      'language': 'Language Learning',
      'islam': 'Islamic Studies',
      'christianity': 'Christian Studies',
      'hinduism': 'Hindu Studies',
      'buddhism': 'Buddhist Studies',
    };
    
    return categoryNames[categoryId] ?? categoryId.toUpperCase();
  }

  // Search books from Supabase
  Future<List<Book>> searchBooks(String query) async {
    try {
      return await _supabaseService.searchBooks(query);
    } catch (e) {
      // Fallback to local search if Supabase fails
      return [];
    }
  }

  // Get category by ID (sync method for backward compatibility)
  Future<Category?> getCategoryById(String categoryId) async {
    try {
      // Check main categories
      final mainCategories = await getMainCategories();
      for (final category in mainCategories) {
        if (category.id == categoryId) return category;
      }

      // Check subcategories
      for (final mainCategory in mainCategories) {
        final subcategories = await getSubcategories(mainCategory.id);
        for (final subcategory in subcategories) {
          if (subcategory.id == categoryId) return subcategory;
        }
      }

      return null;
    } catch (e) {
      // Fallback to local data
      final localCategories = [...CategoryData.mainCategories,
                              ...CategoryData.academicSubcategories,
                              ...CategoryData.trainingSubcategories,
                              ...CategoryData.religionSubcategories];

      try {
        return localCategories.firstWhere((cat) => cat.id == categoryId);
      } catch (e) {
        return null;
      }
    }
  }

  // Get book by ID from Supabase
  Future<Book?> getBookById(String bookId) async {
    try {
      return await _supabaseService.getBookById(bookId);
    } catch (e) {
      return null;
    }
  }
}
