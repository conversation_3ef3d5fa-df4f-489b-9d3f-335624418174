import '../../models/category.dart';
import '../../models/book.dart';

class AppService {
  static final AppService _instance = AppService._internal();
  factory AppService() => _instance;
  AppService._internal();

  // Get main categories
  List<Category> getMainCategories() {
    return CategoryData.mainCategories;
  }

  // Get subcategories by parent category
  List<Category> getSubcategories(String parentCategoryId) {
    switch (parentCategoryId) {
      case 'academic':
        return CategoryData.academicSubcategories;
      case 'training':
        return CategoryData.trainingSubcategories;
      case 'religion':
        return CategoryData.religionSubcategories;
      default:
        return [];
    }
  }

  // Get books by category (mock data for now)
  List<Book> getBooksByCategory(String categoryId) {
    // This would normally fetch from Supabase
    return _getMockBooks(categoryId);
  }

  // Mock books data
  List<Book> _getMockBooks(String categoryId) {
    return [
      Book(
        id: '1',
        title: 'Introduction to ${_getCategoryDisplayName(categoryId)}',
        pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        categoryId: categoryId,
        author: 'Sample Author',
        description: 'A comprehensive guide to ${_getCategoryDisplayName(categoryId)}',
        pageCount: 150,
      ),
      Book(
        id: '2',
        title: 'Advanced ${_getCategoryDisplayName(categoryId)} Concepts',
        pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        categoryId: categoryId,
        author: 'Expert Author',
        description: 'Deep dive into advanced topics',
        pageCount: 250,
      ),
      Book(
        id: '3',
        title: '${_getCategoryDisplayName(categoryId)} Practical Guide',
        pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        categoryId: categoryId,
        author: 'Practical Author',
        description: 'Hands-on approach to learning',
        pageCount: 200,
      ),
    ];
  }

  String _getCategoryDisplayName(String categoryId) {
    final categoryNames = {
      'school': 'School Education',
      'college': 'College Studies',
      'english_medium': 'English Medium',
      'university': 'University',
      'technology': 'Technology',
      'business': 'Business',
      'soft_skills': 'Soft Skills',
      'language': 'Language Learning',
      'islam': 'Islamic Studies',
      'christianity': 'Christian Studies',
      'hinduism': 'Hindu Studies',
      'buddhism': 'Buddhist Studies',
    };
    
    return categoryNames[categoryId] ?? categoryId.toUpperCase();
  }

  // Search books
  List<Book> searchBooks(String query) {
    // This would normally search in Supabase
    final allBooks = <Book>[];
    
    // Get books from all categories
    for (final category in getMainCategories()) {
      final subcategories = getSubcategories(category.id);
      for (final subcategory in subcategories) {
        allBooks.addAll(getBooksByCategory(subcategory.id));
      }
    }
    
    // Filter by query
    return allBooks.where((book) =>
        book.title.toLowerCase().contains(query.toLowerCase()) ||
        (book.author?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
        (book.description?.toLowerCase().contains(query.toLowerCase()) ?? false)
    ).toList();
  }

  // Get category by ID
  Category? getCategoryById(String categoryId) {
    // Check main categories
    for (final category in getMainCategories()) {
      if (category.id == categoryId) return category;
    }
    
    // Check subcategories
    for (final mainCategory in getMainCategories()) {
      final subcategories = getSubcategories(mainCategory.id);
      for (final subcategory in subcategories) {
        if (subcategory.id == categoryId) return subcategory;
      }
    }
    
    return null;
  }

  // Get book by ID
  Book? getBookById(String bookId) {
    final allBooks = <Book>[];
    
    // Get books from all categories
    for (final category in getMainCategories()) {
      final subcategories = getSubcategories(category.id);
      for (final subcategory in subcategories) {
        allBooks.addAll(getBooksByCategory(subcategory.id));
      }
    }
    
    try {
      return allBooks.firstWhere((book) => book.id == bookId);
    } catch (e) {
      return null;
    }
  }
}
