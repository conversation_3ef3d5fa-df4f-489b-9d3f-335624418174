import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// Navigation States
abstract class NavigationState extends Equatable {
  const NavigationState();

  @override
  List<Object?> get props => [];
}

class NavigationInitial extends NavigationState {}

class NavigationLoading extends NavigationState {}

class NavigationSuccess extends NavigationState {
  final String currentRoute;
  final Map<String, dynamic>? params;

  const NavigationSuccess({
    required this.currentRoute,
    this.params,
  });

  @override
  List<Object?> get props => [currentRoute, params];
}

class NavigationError extends NavigationState {
  final String message;

  const NavigationError(this.message);

  @override
  List<Object?> get props => [message];
}

// Navigation Cubit
class NavigationCubit extends Cubit<NavigationState> {
  NavigationCubit() : super(NavigationInitial());

  void navigateToRoute(String route, {Map<String, dynamic>? params}) {
    emit(NavigationLoading());
    
    try {
      // Simulate navigation logic
      emit(NavigationSuccess(currentRoute: route, params: params));
    } catch (e) {
      emit(NavigationError('Failed to navigate to $route'));
    }
  }

  void navigateToCategory(String categoryId) {
    final routes = {
      'academic': '/academic',
      'training': '/training',
      'religion': '/religion',
      'book_lovers': '/book-lovers',
    };

    final route = routes[categoryId];
    if (route != null) {
      navigateToRoute(route, params: {'categoryId': categoryId});
    } else {
      emit(const NavigationError('Invalid category'));
    }
  }

  void navigateToBooks(String categoryId, String subcategoryId) {
    navigateToRoute('/books', params: {
      'categoryId': categoryId,
      'subcategoryId': subcategoryId,
    });
  }

  void navigateToPdfReader(String bookId, String bookTitle) {
    navigateToRoute('/pdf-reader', params: {
      'bookId': bookId,
      'title': bookTitle,
    });
  }

  void goBack() {
    // Handle back navigation
    emit(NavigationInitial());
  }

  void reset() {
    emit(NavigationInitial());
  }
}
