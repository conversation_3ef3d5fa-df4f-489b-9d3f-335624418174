# readtap

# 📚 ReadTap - Digital Reading Platform

ReadTap is a cross-platform reading platform designed for **students, educators, and readers** across **academia, training sectors, religious studies**, and **literature**. It organizes content by institution, program, and subject — allowing users to **browse, download, and read PDFs** with top-tier features like offline caching, bookmarks, auto-scroll, and more.

## 🚀 Features

### ✨ Core Features
- **Beautiful UI** with smooth animations and transitions
- **Category-based organization** (Academic, Training, Religion, Book Lovers)
- **Advanced PDF reader** with zoom, auto-scroll, bookmarks
- **Offline reading** support
- **Responsive design** for all screen sizes

### 📱 User Interface
- **Splash Screen** with animated ReadTap logo
- **Main Categories** with 4 beautifully designed category tiles
- **Subcategory Navigation** for each main category
- **Books List** with search and filter capabilities
- **PDF Reader** with professional reading tools

### 🎨 Design Highlights
- **Material Design 3** with custom color schemes
- **Smooth animations** using flutter_animate
- **Gradient backgrounds** and modern card designs
- **Consistent typography** and spacing
- **Intuitive navigation** with go_router

## 🛠️ Tech Stack

| Component | Technology |
|-----------|------------|
| **Framework** | Flutter 3.22+ |
| **State Management** | flutter_bloc 9.x |
| **Routing** | go_router 15.x |
| **PDF Viewer** | syncfusion_flutter_pdfviewer |
| **Animations** | flutter_animate |
| **Backend** | Supabase (ready for integration) |
| **Local Storage** | Hive (ready for offline support) |
| **HTTP Client** | Dio |

## 📁 Project Structure

```
lib/
├── core/
│   ├── config/
│   │   ├── app_config.dart      # App configuration
│   │   ├── app_theme.dart       # Theme and colors
│   │   └── app_router.dart      # Navigation routes
│   ├── services/
│   │   └── app_service.dart     # Data service layer
│   └── widgets/
│       ├── app_logo.dart        # Reusable logo widget
│       └── category_tile.dart   # Category tile components
├── features/
│   ├── splash/
│   │   └── views/
│   │       └── splash_screen.dart
│   ├── home/
│   │   └── views/
│   │       └── home_screen.dart
│   ├── categories/
│   │   └── views/
│   │       ├── academic_categories_screen.dart
│   │       ├── training_categories_screen.dart
│   │       └── religion_categories_screen.dart
│   ├── books/
│   │   └── views/
│   │       └── books_list_screen.dart
│   ├── reader/
│   │   └── views/
│   │       └── pdf_reader_screen.dart
│   └── navigation/
│       └── bloc/
│           └── navigation_cubit.dart
├── models/
│   ├── category.dart           # Category data model
│   ├── book.dart              # Book data model
│   └── bookmark.dart          # Bookmark data model
└── main.dart
```

## 🎯 App Flow

1. **Splash Screen** (3 seconds)
   - Animated ReadTap logo
   - Loading indicator
   - Auto-navigation to home

2. **Home Screen**
   - 4 main category tiles:
     - 🎓 Academic (Purple)
     - 💼 Training (Teal)
     - 📿 Religion (Green)
     - 📚 Book Lovers (Pink)

3. **Category Screens**
   - **Academic**: School, College, English Medium, University
   - **Training**: Technology, Business, Soft Skills, Language
   - **Religion**: Islam, Christianity, Hinduism, Buddhism

4. **Books List**
   - Display books for selected category
   - Book tiles with title, author, page count
   - Tap to open PDF reader

5. **PDF Reader**
   - Full-featured PDF viewing
   - Zoom in/out controls
   - Auto-scroll functionality
   - Bookmark support
   - Page navigation

## 🚀 Getting Started

### Prerequisites
- Flutter SDK 3.8.1 or higher
- Dart SDK
- Chrome browser (for web testing)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd readtap
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   # For web (recommended for testing)
   flutter run -d chrome

   # For Windows (requires Developer Mode)
   flutter run -d windows

   # For mobile (with connected device)
   flutter run
   ```

## 🎨 Color Scheme

The app uses a carefully designed color palette:

- **Primary Blue**: `#87CEEB` (Splash background)
- **Dark Blue**: `#2E3A59` (Training background)
- **Teal Green**: `#4ECDC4` (Training categories)
- **Light Green**: `#95C93D` (Religion categories)
- **Purple**: `#673AB7` (Academic categories)
- **Pink**: `#E91E63` (Book Lovers)

## 📱 Screenshots Implementation

The app implements all the UI designs from the provided screenshots:

1. ✅ **Splash Screen** - Light blue background with ReadTap logo
2. ✅ **Main Categories** - 4 category tiles with proper colors
3. ✅ **Academic Subcategories** - Blue-themed subcategory buttons
4. ✅ **Training Subcategories** - Teal-themed subcategory buttons
5. ✅ **Religion Subcategories** - Green-themed subcategory buttons
6. ✅ **PDF Reader** - Full-featured reader with controls

## 🔧 Configuration

### App Configuration
Edit `lib/core/config/app_config.dart` to modify:
- Supabase URL and keys
- App settings and constants
- PDF reader configurations

### Theme Customization
Edit `lib/core/config/app_theme.dart` to modify:
- Color schemes
- Typography
- Component themes

## 🧪 Testing

Run tests with:
```bash
flutter test
```

The project includes:
- Widget tests for main screens
- Unit tests for models and services
- Integration test setup (ready for expansion)

## 🚀 Future Enhancements

The app is designed for easy expansion:

- **Supabase Integration**: Backend connection ready
- **Offline Support**: Hive storage implementation ready
- **User Authentication**: Auth flow structure in place
- **Search Functionality**: Search service ready for implementation
- **Bookmarks System**: Bookmark model and UI ready
- **Download Manager**: File handling structure ready

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📞 Support

For support and questions, please open an issue in the repository.

---

**ReadTap** - Your Digital Reading Companion 📚✨

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
