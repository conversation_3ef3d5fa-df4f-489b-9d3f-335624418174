name: readtap
description: "ReadTap - A cross-platform reading platform for students, educators, and readers"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5

  # Backend & Database
  supabase_flutter: ^2.5.6
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Routing
  go_router: ^13.2.4

  # PDF Viewer
  syncfusion_flutter_pdfviewer: ^26.1.41

  # Networking & File Handling
  dio: ^5.4.3+1
  path_provider: ^2.1.3

  # Animations
  flutter_animate: ^4.5.0
  animations: ^2.0.11

  # Utilities
  uuid: ^4.4.0
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.3

  # Code Generation
  hive_generator: ^2.0.1
  build_runner: ^2.4.9

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
