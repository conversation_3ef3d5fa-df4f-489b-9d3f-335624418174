import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/app_config.dart';
import '../../models/category.dart';
import '../../models/book.dart';
import '../../models/bookmark.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  late SupabaseClient _client;
  
  SupabaseClient get client => _client;
  User? get currentUser => _client.auth.currentUser;
  bool get isAuthenticated => currentUser != null;

  // Initialize Supabase
  Future<void> initialize() async {
    await Supabase.initialize(
      url: AppConfig.supabaseUrl,
      anonKey: AppConfig.supabaseAnonKey,
    );
    _client = Supabase.instance.client;
  }

  // Authentication Methods
  Future<AuthResponse> signUpWithEmail(String email, String password, {String? fullName}) async {
    return await _client.auth.signUp(
      email: email,
      password: password,
      data: fullName != null ? {'full_name': fullName} : null,
    );
  }

  Future<AuthResponse> signInWithEmail(String email, String password) async {
    return await _client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    await _client.auth.signOut();
  }

  Future<AuthResponse> signInWithGoogle() async {
    return await _client.auth.signInWithOAuth(OAuthProvider.google);
  }

  Future<AuthResponse> signInWithGithub() async {
    return await _client.auth.signInWithOAuth(OAuthProvider.github);
  }

  // Category Methods
  Future<List<Category>> getMainCategories() async {
    final response = await _client
        .from('categories')
        .select()
        .eq('type', 'main')
        .eq('is_active', true)
        .order('sort_order');
    
    return response.map((json) => Category.fromJson(json)).toList();
  }

  Future<List<Category>> getSubcategories(String parentId) async {
    final response = await _client
        .from('categories')
        .select()
        .eq('parent_id', parentId)
        .eq('is_active', true)
        .order('sort_order');
    
    return response.map((json) => Category.fromJson(json)).toList();
  }

  // Book Methods
  Future<List<Book>> getBooksByCategory(String categoryId, {int limit = 20, int offset = 0}) async {
    final response = await _client
        .from('books')
        .select('*, categories!inner(*)')
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('created_at', ascending: false)
        .range(offset, offset + limit - 1);
    
    return response.map((json) => Book.fromJson(json)).toList();
  }

  Future<List<Book>> getFeaturedBooks({int limit = 10}) async {
    final response = await _client
        .from('books')
        .select('*, categories!inner(*)')
        .eq('is_featured', true)
        .eq('is_active', true)
        .order('view_count', ascending: false)
        .limit(limit);
    
    return response.map((json) => Book.fromJson(json)).toList();
  }

  Future<List<Book>> searchBooks(String query, {int limit = 20}) async {
    final response = await _client
        .rpc('search_books', params: {'search_term': query})
        .limit(limit);
    
    return response.map((json) => Book.fromJson(json)).toList();
  }

  Future<Book?> getBookById(String bookId) async {
    final response = await _client
        .from('books')
        .select('*, categories!inner(*)')
        .eq('id', bookId)
        .eq('is_active', true)
        .maybeSingle();
    
    return response != null ? Book.fromJson(response) : null;
  }

  // Bookmark Methods
  Future<List<Bookmark>> getUserBookmarks(String bookId) async {
    if (!isAuthenticated) throw Exception('User not authenticated');
    
    final response = await _client
        .from('bookmarks')
        .select()
        .eq('user_id', currentUser!.id)
        .eq('book_id', bookId)
        .order('created_at', ascending: false);
    
    return response.map((json) => Bookmark.fromJson(json)).toList();
  }

  Future<Bookmark> addBookmark(String bookId, int pageNumber, {String? note}) async {
    if (!isAuthenticated) throw Exception('User not authenticated');
    
    final response = await _client
        .from('bookmarks')
        .insert({
          'user_id': currentUser!.id,
          'book_id': bookId,
          'page_number': pageNumber,
          'note': note,
        })
        .select()
        .single();
    
    return Bookmark.fromJson(response);
  }

  Future<void> deleteBookmark(String bookmarkId) async {
    if (!isAuthenticated) throw Exception('User not authenticated');
    
    await _client
        .from('bookmarks')
        .delete()
        .eq('id', bookmarkId)
        .eq('user_id', currentUser!.id);
  }

  // Reading Progress Methods
  Future<void> updateReadingProgress(String bookId, int currentPage, int totalPages) async {
    if (!isAuthenticated) throw Exception('User not authenticated');
    
    final progressPercentage = (currentPage / totalPages * 100).clamp(0.0, 100.0);
    
    await _client
        .from('reading_progress')
        .upsert({
          'user_id': currentUser!.id,
          'book_id': bookId,
          'current_page': currentPage,
          'total_pages': totalPages,
          'progress_percentage': progressPercentage,
          'last_read_at': DateTime.now().toIso8601String(),
          'is_completed': progressPercentage >= 100.0,
        });
  }

  Future<Map<String, dynamic>?> getReadingProgress(String bookId) async {
    if (!isAuthenticated) return null;
    
    final response = await _client
        .from('reading_progress')
        .select()
        .eq('user_id', currentUser!.id)
        .eq('book_id', bookId)
        .maybeSingle();
    
    return response;
  }

  // Annotation Methods
  Future<void> saveAnnotation(String bookId, int pageNumber, String type, Map<String, dynamic> data) async {
    if (!isAuthenticated) throw Exception('User not authenticated');
    
    await _client
        .from('annotations')
        .insert({
          'user_id': currentUser!.id,
          'book_id': bookId,
          'page_number': pageNumber,
          'annotation_type': type,
          'annotation_data': data,
        });
  }

  Future<List<Map<String, dynamic>>> getAnnotations(String bookId, int pageNumber) async {
    if (!isAuthenticated) return [];
    
    final response = await _client
        .from('annotations')
        .select()
        .eq('user_id', currentUser!.id)
        .eq('book_id', bookId)
        .eq('page_number', pageNumber)
        .order('created_at');
    
    return List<Map<String, dynamic>>.from(response);
  }

  // User Statistics
  Future<Map<String, dynamic>> getUserStats() async {
    if (!isAuthenticated) throw Exception('User not authenticated');
    
    final response = await _client
        .rpc('get_user_stats', params: {'user_uuid': currentUser!.id});
    
    return response;
  }

  // Increment view count
  Future<void> incrementBookViewCount(String bookId) async {
    await _client.rpc('increment_book_view_count', params: {'book_uuid': bookId});
  }
}
