<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReadTap - Authentication</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📚</div>
        <h1>ReadTap</h1>
        <p>Processing your authentication...</p>
        <div class="spinner"></div>
    </div>

    <script>
        // Handle OAuth callback
        window.addEventListener('load', function() {
            // Check if this is an OAuth callback
            const urlParams = new URLSearchParams(window.location.search);
            const fragment = window.location.hash.substring(1);
            const fragmentParams = new URLSearchParams(fragment);
            
            // Check for authentication tokens
            const accessToken = fragmentParams.get('access_token') || urlParams.get('access_token');
            const refreshToken = fragmentParams.get('refresh_token') || urlParams.get('refresh_token');
            const error = fragmentParams.get('error') || urlParams.get('error');
            
            if (error) {
                document.querySelector('p').textContent = 'Authentication failed: ' + error;
                document.querySelector('.spinner').style.display = 'none';
                return;
            }
            
            if (accessToken) {
                // Store tokens (in a real app, handle this more securely)
                localStorage.setItem('supabase.auth.token', JSON.stringify({
                    access_token: accessToken,
                    refresh_token: refreshToken
                }));
                
                document.querySelector('p').textContent = 'Authentication successful! Redirecting...';
                
                // Redirect to your app or close the window
                setTimeout(() => {
                    // For mobile apps, this might trigger a deep link
                    window.location.href = 'com.readtap.app://login-callback?' + fragment;
                    
                    // Fallback: close the window after a delay
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                }, 1000);
            } else {
                document.querySelector('p').textContent = 'No authentication data received.';
                document.querySelector('.spinner').style.display = 'none';
            }
        });
    </script>
</body>
</html>
